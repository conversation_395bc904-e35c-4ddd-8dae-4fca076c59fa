export interface User {
  id: string;
  email: string;
  role?: string;
}

export interface Commune {
  id: string;
  nom: string;
  codePostal: string;
  latitude: number;
  longitude: number;
  departement: string;
}

export interface PlanEau {
  id: string;
  nom: string;
  latitude: number;
  longitude: number;
  accessible: boolean;
  commune?: string; // nom de la commune
  ecopage: boolean; // indique si l'écopage est possible
}

export interface BaseDash {
  id: string;
  nom: string;
  latitude: number;
  longitude: number;
  capacite: number;
  disponible: boolean;
}

export interface CalculResult {
  commune: Commune;
  planEauProche: PlanEau;
  baseDashProche: BaseDash;
  distanceEau: number;
  distanceDash: number;
  largagesCanadairParHeure: number;
  largagesDashParHeure: number;
  canadairsExact: number; // Nouveau: calcul exact sans arrondi
  dashsExact: number; // Nouveau: calcul exact sans arrondi
  canadairsNecessaires: number;
  dashsNecessaires: number;
  canadairsArrondi: number;
  dashsArrondi: number;
}

export interface InterventionParams {
  communeId: string;
  typeIntervention: 'incendie_foret' | 'incendie_urbain';
  exigencesCanadair: number; // largages/h requis
  exigencesDash: number; // largages/h requis
}

export interface AircraftSettings {
  // Vitesses
  vitesseCroisiere: number; // km/h
  vitesseMaxLargage: number; // km/h
  vitesseMinLargage: number; // km/h
  
  // Capacités
  capaciteReservoir: number; // litres
  tempsRemplissage: number; // minutes
  
  // Manœuvrabilité
  rayonBraquage: number; // mètres
  
  // Performance opérationnelle
  altitudeMaxLargage: number; // mètres
  altitudeMinLargage: number; // mètres
  consommationCarburant: number; // litres/heure
  autonomieVol: number; // heures
}

export interface SimulatorSettings {
  canadair: AircraftSettings;
  dash: AircraftSettings;

  // Paramètres environnementaux
  facteurVent: number; // coefficient 0.5 à 2.0
  facteurVisibilite: number; // coefficient 0.5 à 1.0

  // Paramètres opérationnels
  tempsPreparationMission: number; // minutes
  tempsRetourBase: number; // minutes
  margeSecurite: number; // pourcentage

  // Nouveaux paramètres opérationnels
  exigencesCanadair: number; // largages requis
  exigencesDash: number; // largages requis
  dureeMission: number; // minutes
}

export interface Simulation {
  id: string;
  user_id: string;
  nom: string;
  description?: string;
  commune_data: Commune;
  intervention_params: InterventionParams;
  simulator_settings: SimulatorSettings;
  results: CalculResult;
  created_at: string;
  updated_at: string;
}

export interface SaveSimulationData {
  nom: string;
  description?: string;
  commune_data: Commune;
  intervention_params: InterventionParams;
  simulator_settings: SimulatorSettings;
  results: CalculResult;
}