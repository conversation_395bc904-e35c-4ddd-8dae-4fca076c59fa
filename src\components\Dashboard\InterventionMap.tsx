import React, { useEffect, useRef, useState } from 'react';
import { CalculResult, MapLayer, WeatherData } from '../../types';
import { AdvancedMap } from './AdvancedMap';
import { MapLayerControl } from './MapLayerControl';
import { WeatherPanel } from './WeatherPanel';
import { getWeatherData } from '../../services/mapDataService';
import { Settings, Maximize2, Minimize2 } from 'lucide-react';

interface InterventionMapProps {
  results?: CalculResult;
  height?: number;
}

export function InterventionMap({ results, height = 500 }: InterventionMapProps) {
  const [showControls, setShowControls] = useState(true);
  const [showWeather, setShowWeather] = useState(true);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [weatherData, setWeatherData] = useState<WeatherData | null>(null);
  const [mapLayers, setMapLayers] = useState<MapLayer[]>([
    {
      id: 'risk_zones',
      name: 'Zones de risque',
      type: 'risk_zones',
      visible: true,
      opacity: 0.6
    },
    {
      id: 'weather',
      name: 'Données météo',
      type: 'weather',
      visible: false,
      opacity: 0.8
    },
    {
      id: 'flight_paths',
      name: 'Trajectoires de vol',
      type: 'flight_paths',
      visible: true,
      opacity: 0.9
    },
    {
      id: 'terrain',
      name: 'Relief',
      type: 'terrain',
      visible: false,
      opacity: 0.7
    },
    {
      id: 'vegetation',
      name: 'Végétation',
      type: 'vegetation',
      visible: false,
      opacity: 0.5
    }
  ]);

  // Charger les données météo quand les résultats changent
  useEffect(() => {
    if (results) {
      getWeatherData(results.commune.latitude, results.commune.longitude)
        .then(setWeatherData)
        .catch(console.error);
    }
  }, [results]);

  const handleLayerToggle = (layerId: string) => {
    setMapLayers(prev => prev.map(layer =>
      layer.id === layerId
        ? { ...layer, visible: !layer.visible }
        : layer
    ));
  };

  const handleOpacityChange = (layerId: string, opacity: number) => {
    setMapLayers(prev => prev.map(layer =>
      layer.id === layerId
        ? { ...layer, opacity }
        : layer
    ));
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  const mapHeight = isFullscreen ? window.innerHeight - 100 : height;

  return (
    <div className={`bg-white rounded-lg shadow-md overflow-hidden ${
      isFullscreen ? 'fixed inset-4 z-50' : ''
    }`}>
      {/* En-tête avec contrôles */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-800">
            Cartographie avancée
          </h3>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setShowControls(!showControls)}
              className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors"
              title="Afficher/masquer les contrôles"
            >
              <Settings className="h-4 w-4" />
            </button>
            <button
              onClick={toggleFullscreen}
              className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors"
              title={isFullscreen ? "Quitter le plein écran" : "Plein écran"}
            >
              {isFullscreen ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
            </button>
          </div>
        </div>
      </div>

      <div className="relative">
        {/* Carte principale */}
        <div className="flex">
          <div className={`flex-1 ${showControls ? 'mr-80' : ''}`}>
            <AdvancedMap
              results={results}
              height={mapHeight}
              activeLayers={mapLayers}
              onLayerToggle={handleLayerToggle}
            />
          </div>

          {/* Panneau de contrôles latéral */}
          {showControls && (
            <div className="absolute top-0 right-0 w-80 h-full bg-gray-50 border-l border-gray-200 overflow-y-auto">
              <div className="p-4 space-y-4">
                {/* Contrôles des couches */}
                <MapLayerControl
                  layers={mapLayers}
                  onLayerToggle={handleLayerToggle}
                  onOpacityChange={handleOpacityChange}
                />

                {/* Données météo */}
                {weatherData && results && (
                  <WeatherPanel
                    weather={weatherData}
                    location={results.commune.nom}
                  />
                )}

                {/* Informations sur les trajectoires */}
                {results && (
                  <div className="bg-white rounded-lg shadow-md p-4">
                    <h4 className="font-semibold text-gray-800 mb-3">Informations de vol</h4>
                    <div className="space-y-3 text-sm">
                      <div className="border-l-4 border-blue-600 pl-3">
                        <div className="font-medium text-blue-800">Canadair</div>
                        <div className="text-gray-600">
                          Distance: {results.distanceEau} km<br/>
                          Largages/h: {results.largagesCanadairParHeure}
                        </div>
                      </div>
                      <div className="border-l-4 border-green-600 pl-3">
                        <div className="font-medium text-green-800">Dash</div>
                        <div className="text-gray-600">
                          Distance: {results.distanceDash} km<br/>
                          Largages/h: {results.largagesDashParHeure}
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Légende simplifiée en bas */}
      {!showControls && (
        <div className="p-4 bg-gray-50 border-t border-gray-200">
          <div className="flex items-center justify-between">
            <h4 className="font-medium text-gray-800">Légende</h4>
            <div className="flex items-center space-x-4 text-sm">
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 bg-red-600 rounded-full flex items-center justify-center text-white text-xs">🔥</div>
                <span>Intervention</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 bg-blue-600 rounded-full flex items-center justify-center text-white text-xs">💧</div>
                <span>Plan d'eau</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 bg-green-600 rounded-full flex items-center justify-center text-white text-xs">✈️</div>
                <span>Base</span>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}