import React, { useState } from 'react';
import { Header } from './components/Layout/Header';
import { LoginForm } from './components/Auth/LoginForm';
import { Dashboard } from './components/Dashboard/Dashboard';
import { SettingsPage } from './components/Settings/SettingsPage';
import { useAuth } from './hooks/useAuth';

type AppView = 'dashboard' | 'settings';

function App() {
  const { user, loading, error, login, logout } = useAuth();
  const [currentView, setCurrentView] = useState<AppView>('dashboard');

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Chargement...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return <LoginForm onLogin={login} loading={loading} error={error} />;
  }

  if (currentView === 'settings') {
    return <SettingsPage onBack={() => setCurrentView('dashboard')} />;
  }

  return (
    <div className="min-h-screen bg-gray-100">
      <Header 
        user={user} 
        onLogout={logout}
        onSettingsClick={() => setCurrentView('settings')}
      />
      <Dashboard />
    </div>
  );
}

export default App;