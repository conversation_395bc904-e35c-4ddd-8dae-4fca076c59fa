@tailwind base;
@tailwind components;
@tailwind utilities;

/* Styles pour la cartographie avancée */
.custom-fire-icon,
.custom-water-icon,
.custom-base-icon {
  border: none !important;
  background: transparent !important;
}

/* Styles pour les sliders */
.slider::-webkit-slider-thumb {
  appearance: none;
  height: 16px;
  width: 16px;
  border-radius: 50%;
  background: #DC2626;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.slider::-moz-range-thumb {
  height: 16px;
  width: 16px;
  border-radius: 50%;
  background: #DC2626;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Animation pour les zones de risque */
@keyframes pulse-risk {
  0%, 100% {
    opacity: 0.6;
  }
  50% {
    opacity: 0.8;
  }
}

.risk-zone-animation {
  animation: pulse-risk 3s ease-in-out infinite;
}

/* Styles pour les popups Leaflet */
.leaflet-popup-content {
  margin: 8px 12px !important;
  line-height: 1.4 !important;
}

.leaflet-popup-content-wrapper {
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* Amélioration des contrôles de zoom */
.leaflet-control-zoom a {
  background-color: #ffffff !important;
  border: 1px solid #d1d5db !important;
  color: #374151 !important;
}

.leaflet-control-zoom a:hover {
  background-color: #f9fafb !important;
  border-color: #9ca3af !important;
}

/* Custom slider styles */
.slider::-webkit-slider-thumb {
  appearance: none;
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: #DC2626;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.slider::-moz-range-thumb {
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: #DC2626;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}