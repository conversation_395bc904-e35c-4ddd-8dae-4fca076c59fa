import React, { useEffect, useRef, useState } from 'react';
import { AdvancedMapProps, RiskZone, WeatherData, FlightPath, MapLayer } from '../../types';
import { getRiskZones, getWeatherData, calculateFlightPaths } from '../../services/mapDataService';

export function AdvancedMap({
  results,
  height = 500,
  activeLayers = [],
  onLayerToggle
}: AdvancedMapProps) {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<any>(null);
  const layersRef = useRef<any[]>([]);
  const [riskZones, setRiskZones] = useState<RiskZone[]>([]);
  const [weatherData, setWeatherData] = useState<WeatherData | null>(null);
  const [flightPaths, setFlightPaths] = useState<FlightPath[]>([]);
  const [loading, setLoading] = useState(false);

  // Initialiser la carte
  useEffect(() => {
    if (!mapRef.current) return;

    const map = (window as any).L.map(mapRef.current).setView([43.8367, 4.3601], 8);
    mapInstanceRef.current = map;

    // Ajouter les tuiles OpenStreetMap par défaut
    (window as any).L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '© OpenStreetMap contributors'
    }).addTo(map);

    // Charger les données initiales
    loadMapData();

    return () => {
      if (mapInstanceRef.current) {
        mapInstanceRef.current.remove();
      }
    };
  }, []);

  // Charger les données cartographiques
  const loadMapData = async () => {
    setLoading(true);
    try {
      const zones = await getRiskZones();
      setRiskZones(zones);
    } catch (error) {
      console.error('Erreur lors du chargement des données cartographiques:', error);
    } finally {
      setLoading(false);
    }
  };

  // Mettre à jour la carte quand les résultats changent
  useEffect(() => {
    if (!results || !mapInstanceRef.current) return;

    const map = mapInstanceRef.current;
    const L = (window as any).L;

    // Nettoyer les marqueurs existants
    map.eachLayer((layer: any) => {
      if (layer instanceof L.Marker || layer instanceof L.Polyline || layer instanceof L.Polygon) {
        map.removeLayer(layer);
      }
    });

    // Créer les icônes personnalisées
    const fireIcon = L.divIcon({
      html: '<div style="background-color: #DC2626; color: white; border-radius: 50%; width: 28px; height: 28px; display: flex; align-items: center; justify-content: center; font-size: 16px; box-shadow: 0 2px 4px rgba(0,0,0,0.3);">🔥</div>',
      className: 'custom-fire-icon',
      iconSize: [28, 28],
      iconAnchor: [14, 14]
    });

    const waterIcon = L.divIcon({
      html: '<div style="background-color: #2563EB; color: white; border-radius: 50%; width: 24px; height: 24px; display: flex; align-items: center; justify-content: center; font-size: 14px; box-shadow: 0 2px 4px rgba(0,0,0,0.3);">💧</div>',
      className: 'custom-water-icon',
      iconSize: [24, 24],
      iconAnchor: [12, 12]
    });

    const baseIcon = L.divIcon({
      html: '<div style="background-color: #059669; color: white; border-radius: 50%; width: 24px; height: 24px; display: flex; align-items: center; justify-content: center; font-size: 14px; box-shadow: 0 2px 4px rgba(0,0,0,0.3);">✈️</div>',
      className: 'custom-base-icon',
      iconSize: [24, 24],
      iconAnchor: [12, 12]
    });

    // Ajouter les marqueurs principaux
    const fireMarker = L.marker([results.commune.latitude, results.commune.longitude], { icon: fireIcon })
      .addTo(map)
      .bindPopup(`
        <div class="p-2">
          <strong class="text-red-600">🔥 Zone d'intervention</strong><br/>
          <div class="mt-1">
            <strong>${results.commune.nom}</strong><br/>
            <span class="text-sm text-gray-600">${results.commune.departement}</span>
          </div>
        </div>
      `);

    const waterMarker = L.marker([results.planEauProche.latitude, results.planEauProche.longitude], { icon: waterIcon })
      .addTo(map)
      .bindPopup(`
        <div class="p-2">
          <strong class="text-blue-600">💧 Plan d'eau</strong><br/>
          <div class="mt-1">
            <strong>${results.planEauProche.nom}</strong><br/>
            <span class="text-sm text-gray-600">Distance: ${results.distanceEau} km</span><br/>
            <span class="text-sm ${results.planEauProche.accessible ? 'text-green-600' : 'text-red-600'}">
              ${results.planEauProche.accessible ? '✓ Accessible' : '✗ Non accessible'}
            </span>
          </div>
        </div>
      `);

    const baseMarker = L.marker([results.baseDashProche.latitude, results.baseDashProche.longitude], { icon: baseIcon })
      .addTo(map)
      .bindPopup(`
        <div class="p-2">
          <strong class="text-green-600">✈️ Base Dash</strong><br/>
          <div class="mt-1">
            <strong>${results.baseDashProche.nom}</strong><br/>
            <span class="text-sm text-gray-600">Distance: ${results.distanceDash} km</span><br/>
            <span class="text-sm text-gray-600">Capacité: ${results.baseDashProche.capacite} appareils</span>
          </div>
        </div>
      `);

    // Calculer et afficher les trajectoires de vol
    const canadairPath = calculateFlightPaths(
      results.planEauProche.latitude,
      results.planEauProche.longitude,
      results.commune.latitude,
      results.commune.longitude,
      'canadair'
    );

    const dashPath = calculateFlightPaths(
      results.baseDashProche.latitude,
      results.baseDashProche.longitude,
      results.commune.latitude,
      results.commune.longitude,
      'dash'
    );

    setFlightPaths([canadairPath, dashPath]);

    // Ajouter les lignes de trajectoire
    const waterLine = L.polyline(canadairPath.coordinates, { 
      color: '#2563EB', 
      weight: 4, 
      opacity: 0.8,
      dashArray: '10, 5'
    }).addTo(map);

    const baseLine = L.polyline(dashPath.coordinates, { 
      color: '#059669', 
      weight: 4, 
      opacity: 0.8,
      dashArray: '10, 5'
    }).addTo(map);

    // Ajouter les popups aux trajectoires
    waterLine.bindPopup(`
      <div class="p-2">
        <strong class="text-blue-600">Trajectoire Canadair</strong><br/>
        <span class="text-sm text-gray-600">Distance: ${canadairPath.distance} km</span><br/>
        <span class="text-sm text-gray-600">Temps estimé: ${canadairPath.estimatedTime} min</span><br/>
        <span class="text-sm text-gray-600">Consommation: ${canadairPath.fuelConsumption} L</span>
      </div>
    `);

    baseLine.bindPopup(`
      <div class="p-2">
        <strong class="text-green-600">Trajectoire Dash</strong><br/>
        <span class="text-sm text-gray-600">Distance: ${dashPath.distance} km</span><br/>
        <span class="text-sm text-gray-600">Temps estimé: ${dashPath.estimatedTime} min</span><br/>
        <span class="text-sm text-gray-600">Consommation: ${dashPath.fuelConsumption} L</span>
      </div>
    `);

    // Charger les données météo pour la zone d'intervention
    getWeatherData(results.commune.latitude, results.commune.longitude)
      .then(setWeatherData)
      .catch(console.error);

    // Ajuster la vue pour inclure tous les points
    const group = L.featureGroup([fireMarker, waterMarker, baseMarker]);
    map.fitBounds(group.getBounds().pad(0.1));

  }, [results]);

  // Fonction pour nettoyer toutes les couches personnalisées
  const clearCustomLayers = () => {
    if (!mapInstanceRef.current) return;

    layersRef.current.forEach(layer => {
      mapInstanceRef.current.removeLayer(layer);
    });
    layersRef.current = [];
  };

  // Fonction pour ajouter les zones de risque
  const addRiskZones = (opacity: number) => {
    if (!mapInstanceRef.current || !riskZones.length) return;

    const map = mapInstanceRef.current;
    const L = (window as any).L;

    console.log('Ajout des zones de risque:', riskZones.length, 'zones');

    riskZones.forEach((zone, index) => {
      const color = getRiskZoneColor(zone.type);
      console.log(`Zone ${index}: ${zone.nom}, couleur: ${color}, opacité: ${opacity}`);

      const polygon = L.polygon(zone.coordinates, {
        color: color,
        fillColor: color,
        fillOpacity: Math.max(opacity * 0.4, 0.2), // Opacité minimum pour la visibilité
        weight: 3,
        opacity: Math.max(opacity, 0.6), // Bordure toujours visible
        className: 'risk-zone-layer',
        dashArray: zone.type === 'tres_eleve' ? '5, 5' : null // Pointillés pour très élevé
      }).addTo(map);

      polygon.bindPopup(`
        <div class="p-2">
          <strong class="text-red-600">⚠️ Zone de risque</strong><br/>
          <div class="mt-1">
            <strong>${zone.nom}</strong><br/>
            <span class="text-sm text-gray-600">Niveau: ${getRiskLevelText(zone.type)}</span><br/>
            <span class="text-sm text-gray-600">Facteur: ${zone.facteurRisque}</span><br/>
            ${zone.description ? `<span class="text-sm text-gray-500">${zone.description}</span>` : ''}
          </div>
        </div>
      `);

      layersRef.current.push(polygon);
    });
  };

  // Fonction pour ajouter des marqueurs météo
  const addWeatherMarkers = (opacity: number) => {
    if (!mapInstanceRef.current || !results) return;

    const map = mapInstanceRef.current;
    const L = (window as any).L;

    // Décaler légèrement le marqueur météo pour éviter la superposition
    const offsetLat = results.commune.latitude + 0.005; // Décalage vers le nord
    const offsetLon = results.commune.longitude + 0.005; // Décalage vers l'est

    // Ajouter un marqueur météo décalé
    const weatherIcon = L.divIcon({
      html: `<div style="background-color: rgba(59, 130, 246, ${opacity}); color: white; border-radius: 8px; width: 36px; height: 24px; display: flex; align-items: center; justify-content: center; font-size: 12px; box-shadow: 0 2px 4px rgba(0,0,0,0.3); border: 2px solid white;">🌤️</div>`,
      className: 'weather-marker',
      iconSize: [36, 24],
      iconAnchor: [18, 12]
    });

    const weatherMarker = L.marker([offsetLat, offsetLon], {
      icon: weatherIcon,
      opacity: opacity
    }).addTo(map);

    if (weatherData) {
      weatherMarker.bindPopup(`
        <div class="p-2">
          <strong class="text-blue-600">🌤️ Conditions météo</strong><br/>
          <div class="mt-1 text-sm">
            <div>🌡️ ${weatherData.temperature}°C</div>
            <div>💨 ${weatherData.windSpeed} km/h</div>
            <div>👁️ ${weatherData.visibility} km</div>
            <div>💧 ${weatherData.humidity}%</div>
          </div>
        </div>
      `);
    }

    layersRef.current.push(weatherMarker);
  };

  // Mettre à jour les couches quand les paramètres changent
  useEffect(() => {
    if (!mapInstanceRef.current) return;

    console.log('Mise à jour des couches:', activeLayers);

    // Nettoyer les couches existantes
    clearCustomLayers();

    // Ajouter les couches actives
    activeLayers.forEach(layer => {
      if (!layer.visible) return;

      console.log(`Ajout de la couche ${layer.id} avec opacité ${layer.opacity}`);

      switch (layer.id) {
        case 'risk_zones':
          addRiskZones(layer.opacity);
          break;
        case 'weather':
          addWeatherMarkers(layer.opacity);
          break;
        case 'terrain':
          // Ajouter une couche de relief (simulation)
          addTerrainLayer(layer.opacity);
          break;
        case 'vegetation':
          // Ajouter une couche de végétation (simulation)
          addVegetationLayer(layer.opacity);
          break;
        case 'flight_paths':
          // Ajouter des trajectoires de vol améliorées
          addEnhancedFlightPaths(layer.opacity);
          break;
        default:
          break;
      }
    });

  }, [activeLayers, riskZones, weatherData, results]);

  // Fonction pour ajouter une couche de terrain simulée
  const addTerrainLayer = (opacity: number) => {
    if (!mapInstanceRef.current) return;

    const map = mapInstanceRef.current;
    const L = (window as any).L;

    console.log('Ajout de la couche terrain avec opacité:', opacity);

    // Ajouter une couche de tuiles de terrain avec opacité (relief topographique)
    const terrainLayer = L.tileLayer('https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png', {
      attribution: '© OpenTopoMap (Relief topographique)',
      opacity: Math.max(opacity * 0.8, 0.3), // Opacité minimum pour voir le relief
      className: 'terrain-layer'
    }).addTo(map);

    layersRef.current.push(terrainLayer);

    // Ajouter un marqueur explicatif si c'est la première fois
    if (results) {
      const infoIcon = L.divIcon({
        html: `<div style="background-color: rgba(139, 69, 19, ${opacity}); color: white; border-radius: 4px; padding: 2px 6px; font-size: 10px; font-weight: bold; box-shadow: 0 1px 3px rgba(0,0,0,0.3);">RELIEF</div>`,
        className: 'terrain-info',
        iconSize: [50, 20],
        iconAnchor: [25, 10]
      });

      const infoMarker = L.marker([
        results.commune.latitude - 0.02,
        results.commune.longitude - 0.02
      ], {
        icon: infoIcon,
        opacity: opacity
      }).addTo(map);

      infoMarker.bindPopup(`
        <div class="p-2">
          <strong class="text-amber-600">🏔️ Relief topographique</strong><br/>
          <div class="mt-1 text-sm">
            <div>Courbes de niveau et altitudes</div>
            <div>Utile pour planifier les trajectoires</div>
            <div>Source: OpenTopoMap</div>
          </div>
        </div>
      `);

      layersRef.current.push(infoMarker);
    }
  };

  // Fonction pour ajouter une couche de végétation simulée
  const addVegetationLayer = (opacity: number) => {
    if (!mapInstanceRef.current || !results) return;

    const map = mapInstanceRef.current;
    const L = (window as any).L;

    console.log('Ajout de la couche végétation avec opacité:', opacity);

    // Simuler des zones de végétation plus visibles autour de la zone d'intervention
    const vegetationZones = [
      {
        center: [results.commune.latitude + 0.02, results.commune.longitude + 0.02],
        radius: 3000,
        type: 'dense',
        color: '#16a34a'
      },
      {
        center: [results.commune.latitude - 0.015, results.commune.longitude + 0.015],
        radius: 2500,
        type: 'moderate',
        color: '#65a30d'
      },
      {
        center: [results.commune.latitude + 0.01, results.commune.longitude - 0.02],
        radius: 2000,
        type: 'sparse',
        color: '#84cc16'
      }
    ];

    vegetationZones.forEach((zone, index) => {
      console.log(`Zone végétation ${index}: ${zone.type}, rayon: ${zone.radius}m`);

      const circle = L.circle(zone.center, {
        color: zone.color,
        fillColor: zone.color,
        fillOpacity: Math.max(opacity * 0.3, 0.15), // Opacité minimum
        radius: zone.radius,
        weight: 2,
        opacity: Math.max(opacity, 0.5), // Bordure visible
        dashArray: zone.type === 'dense' ? null : '10, 5' // Pointillés pour modérée/sparse
      }).addTo(map);

      circle.bindPopup(`
        <div class="p-2">
          <strong class="text-green-600">🌲 Végétation</strong><br/>
          <div class="mt-1">
            <span class="text-sm text-gray-600">Type: ${
              zone.type === 'dense' ? 'Dense' :
              zone.type === 'moderate' ? 'Modérée' : 'Clairsemée'
            }</span><br/>
            <span class="text-sm text-gray-600">Rayon: ${zone.radius}m</span>
          </div>
        </div>
      `);

      layersRef.current.push(circle);
    });
  };

  // Fonction pour ajouter des trajectoires de vol améliorées
  const addEnhancedFlightPaths = (opacity: number) => {
    if (!mapInstanceRef.current || !results) return;

    const map = mapInstanceRef.current;
    const L = (window as any).L;

    console.log('Ajout des trajectoires de vol améliorées avec opacité:', opacity);

    // Trajectoire Canadair avec points intermédiaires
    const canadairPath = [
      [results.planEauProche.latitude, results.planEauProche.longitude],
      [(results.planEauProche.latitude + results.commune.latitude) / 2,
       (results.planEauProche.longitude + results.commune.longitude) / 2],
      [results.commune.latitude, results.commune.longitude]
    ];

    const canadairLine = L.polyline(canadairPath, {
      color: '#2563EB',
      weight: 4,
      opacity: opacity,
      dashArray: '15, 10',
      className: 'canadair-path'
    }).addTo(map);

    // Ajouter des marqueurs directionnels simples
    const midPointCanadair = [
      (results.planEauProche.latitude + results.commune.latitude) / 2,
      (results.planEauProche.longitude + results.commune.longitude) / 2
    ];

    const canadairArrow = L.divIcon({
      html: `<div style="color: #2563EB; font-size: 16px; opacity: ${opacity};">➤</div>`,
      className: 'flight-arrow',
      iconSize: [16, 16],
      iconAnchor: [8, 8]
    });

    const canadairMarker = L.marker(midPointCanadair, {
      icon: canadairArrow,
      opacity: opacity
    }).addTo(map);

    // Trajectoire Dash
    const dashPath = [
      [results.baseDashProche.latitude, results.baseDashProche.longitude],
      [(results.baseDashProche.latitude + results.commune.latitude) / 2,
       (results.baseDashProche.longitude + results.commune.longitude) / 2],
      [results.commune.latitude, results.commune.longitude]
    ];

    const dashLine = L.polyline(dashPath, {
      color: '#16a34a',
      weight: 4,
      opacity: opacity,
      dashArray: '15, 10',
      className: 'dash-path'
    }).addTo(map);

    const midPointDash = [
      (results.baseDashProche.latitude + results.commune.latitude) / 2,
      (results.baseDashProche.longitude + results.commune.longitude) / 2
    ];

    const dashArrow = L.divIcon({
      html: `<div style="color: #16a34a; font-size: 16px; opacity: ${opacity};">➤</div>`,
      className: 'flight-arrow',
      iconSize: [16, 16],
      iconAnchor: [8, 8]
    });

    const dashMarker = L.marker(midPointDash, {
      icon: dashArrow,
      opacity: opacity
    }).addTo(map);

    // Popups pour les trajectoires
    canadairLine.bindPopup(`
      <div class="p-2">
        <strong class="text-blue-600">✈️ Trajectoire Canadair</strong><br/>
        <div class="mt-1 text-sm">
          <div>Distance: ${results.distanceEau} km</div>
          <div>Largages/h: ${results.largagesCanadairParHeure}</div>
          <div>Plan d'eau → Zone d'intervention</div>
        </div>
      </div>
    `);

    dashLine.bindPopup(`
      <div class="p-2">
        <strong class="text-green-600">✈️ Trajectoire Dash</strong><br/>
        <div class="mt-1 text-sm">
          <div>Distance: ${results.distanceDash} km</div>
          <div>Largages/h: ${results.largagesDashParHeure}</div>
          <div>Base → Zone d'intervention</div>
        </div>
      </div>
    `);

    layersRef.current.push(canadairLine, canadairMarker, dashLine, dashMarker);
  };

  const getRiskZoneColor = (type: string): string => {
    switch (type) {
      case 'tres_eleve': return '#DC2626';
      case 'eleve': return '#F97316';
      case 'modere': return '#EAB308';
      case 'faible': return '#16A34A';
      default: return '#6B7280';
    }
  };

  const getRiskLevelText = (type: string): string => {
    switch (type) {
      case 'tres_eleve': return 'Très élevé';
      case 'eleve': return 'Élevé';
      case 'modere': return 'Modéré';
      case 'faible': return 'Faible';
      default: return 'Inconnu';
    }
  };

  return (
    <div className="relative">
      <div ref={mapRef} style={{ height: `${height}px`, width: '100%' }} />
      
      {loading && (
        <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600"></div>
        </div>
      )}
    </div>
  );
}
