import React from 'react';
import { 
  Layers, 
  AlertTriangle, 
  Cloud, 
  Navigation, 
  Mountain, 
  Trees,
  Eye,
  EyeOff
} from 'lucide-react';
import { MapLayer } from '../../types';

interface MapLayerControlProps {
  layers: MapLayer[];
  onLayerToggle: (layerId: string) => void;
  onOpacityChange: (layerId: string, opacity: number) => void;
}

export function MapLayerControl({ layers, onLayerToggle, onOpacityChange }: MapLayerControlProps) {
  const getLayerIcon = (type: string) => {
    switch (type) {
      case 'risk_zones':
        return <AlertTriangle className="h-4 w-4" />;
      case 'weather':
        return <Cloud className="h-4 w-4" />;
      case 'flight_paths':
        return <Navigation className="h-4 w-4" />;
      case 'terrain':
        return <Mountain className="h-4 w-4" />;
      case 'vegetation':
        return <Trees className="h-4 w-4" />;
      default:
        return <Layers className="h-4 w-4" />;
    }
  };

  const getLayerColor = (type: string) => {
    switch (type) {
      case 'risk_zones':
        return 'text-red-600';
      case 'weather':
        return 'text-blue-600';
      case 'flight_paths':
        return 'text-purple-600';
      case 'terrain':
        return 'text-amber-600';
      case 'vegetation':
        return 'text-green-600';
      default:
        return 'text-gray-600';
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-4 space-y-4">
      <div className="flex items-center space-x-2">
        <Layers className="h-5 w-5 text-gray-700" />
        <h3 className="font-semibold text-gray-800">Couches cartographiques</h3>
      </div>

      <div className="space-y-3">
        {layers.map((layer) => (
          <div key={layer.id} className="space-y-2">
            {/* Toggle de visibilité */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <button
                  onClick={() => onLayerToggle(layer.id)}
                  className={`p-1 rounded transition-colors ${
                    layer.visible 
                      ? 'text-gray-700 hover:bg-gray-100' 
                      : 'text-gray-400 hover:bg-gray-50'
                  }`}
                >
                  {layer.visible ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
                </button>
                
                <div className={`${getLayerColor(layer.type)}`}>
                  {getLayerIcon(layer.type)}
                </div>
                
                <span className={`text-sm font-medium ${
                  layer.visible ? 'text-gray-800' : 'text-gray-500'
                }`}>
                  {layer.name}
                </span>
              </div>

              <div className="text-xs text-gray-500">
                {Math.round(layer.opacity * 100)}%
              </div>
            </div>

            {/* Slider d'opacité */}
            {layer.visible && (
              <div className="ml-8">
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  value={layer.opacity}
                  onChange={(e) => onOpacityChange(layer.id, parseFloat(e.target.value))}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                  style={{
                    background: `linear-gradient(to right, #e5e7eb 0%, #e5e7eb ${layer.opacity * 100}%, #d1d5db ${layer.opacity * 100}%, #d1d5db 100%)`
                  }}
                />
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Légende des zones de risque */}
      <div className="border-t pt-3 mt-4">
        <h4 className="text-sm font-medium text-gray-700 mb-2">Niveaux de risque</h4>
        <div className="space-y-1 text-xs">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-red-600 rounded"></div>
            <span>Très élevé</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-orange-500 rounded"></div>
            <span>Élevé</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-yellow-500 rounded"></div>
            <span>Modéré</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-green-500 rounded"></div>
            <span>Faible</span>
          </div>
        </div>
      </div>

      {/* Informations sur les trajectoires */}
      <div className="border-t pt-3">
        <h4 className="text-sm font-medium text-gray-700 mb-2">Trajectoires</h4>
        <div className="space-y-1 text-xs">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-1 bg-blue-600"></div>
            <span>Canadair</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-1 bg-green-600"></div>
            <span>Dash</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-1 bg-purple-600 border-dashed border"></div>
            <span>Trajectoire optimisée</span>
          </div>
        </div>
      </div>
    </div>
  );
}
