import React from 'react';
import { 
  Cloud, 
  Thermometer, 
  Droplets, 
  Wind, 
  Eye, 
  Gauge,
  Navigation
} from 'lucide-react';
import { WeatherData } from '../../types';

interface WeatherPanelProps {
  weather: WeatherData;
  location: string;
}

export function WeatherPanel({ weather, location }: WeatherPanelProps) {
  const getWindDirection = (degrees: number): string => {
    const directions = ['N', 'NNE', 'NE', 'ENE', 'E', 'ESE', 'SE', 'SSE', 'S', 'SSW', 'SW', 'WSW', 'W', 'WNW', 'NW', 'NNW'];
    const index = Math.round(degrees / 22.5) % 16;
    return directions[index];
  };

  const getWindSpeedColor = (speed: number): string => {
    if (speed < 10) return 'text-green-600';
    if (speed < 20) return 'text-yellow-600';
    if (speed < 30) return 'text-orange-600';
    return 'text-red-600';
  };

  const getVisibilityColor = (visibility: number): string => {
    if (visibility > 15) return 'text-green-600';
    if (visibility > 10) return 'text-yellow-600';
    if (visibility > 5) return 'text-orange-600';
    return 'text-red-600';
  };

  const getTemperatureColor = (temp: number): string => {
    if (temp < 10) return 'text-blue-600';
    if (temp < 25) return 'text-green-600';
    if (temp < 35) return 'text-orange-600';
    return 'text-red-600';
  };

  const formatTime = (timestamp: string): string => {
    return new Date(timestamp).toLocaleTimeString('fr-FR', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-4 space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Cloud className="h-5 w-5 text-blue-600" />
          <h3 className="font-semibold text-gray-800">Conditions météo</h3>
        </div>
        <div className="text-xs text-gray-500">
          {formatTime(weather.timestamp)}
        </div>
      </div>

      <div className="text-sm text-gray-600 mb-3">
        📍 {location}
      </div>

      <div className="grid grid-cols-2 gap-4">
        {/* Température */}
        <div className="flex items-center space-x-3">
          <div className={`p-2 rounded-lg bg-gray-50 ${getTemperatureColor(weather.temperature)}`}>
            <Thermometer className="h-4 w-4" />
          </div>
          <div>
            <div className="text-sm text-gray-600">Température</div>
            <div className={`font-semibold ${getTemperatureColor(weather.temperature)}`}>
              {weather.temperature}°C
            </div>
          </div>
        </div>

        {/* Humidité */}
        <div className="flex items-center space-x-3">
          <div className="p-2 rounded-lg bg-gray-50 text-blue-600">
            <Droplets className="h-4 w-4" />
          </div>
          <div>
            <div className="text-sm text-gray-600">Humidité</div>
            <div className="font-semibold text-blue-600">
              {weather.humidity}%
            </div>
          </div>
        </div>

        {/* Vent */}
        <div className="flex items-center space-x-3">
          <div className={`p-2 rounded-lg bg-gray-50 ${getWindSpeedColor(weather.windSpeed)}`}>
            <Wind className="h-4 w-4" />
          </div>
          <div>
            <div className="text-sm text-gray-600">Vent</div>
            <div className={`font-semibold ${getWindSpeedColor(weather.windSpeed)}`}>
              {weather.windSpeed} km/h
            </div>
            <div className="text-xs text-gray-500 flex items-center space-x-1">
              <Navigation 
                className="h-3 w-3" 
                style={{ transform: `rotate(${weather.windDirection}deg)` }}
              />
              <span>{getWindDirection(weather.windDirection)}</span>
            </div>
          </div>
        </div>

        {/* Visibilité */}
        <div className="flex items-center space-x-3">
          <div className={`p-2 rounded-lg bg-gray-50 ${getVisibilityColor(weather.visibility)}`}>
            <Eye className="h-4 w-4" />
          </div>
          <div>
            <div className="text-sm text-gray-600">Visibilité</div>
            <div className={`font-semibold ${getVisibilityColor(weather.visibility)}`}>
              {weather.visibility} km
            </div>
          </div>
        </div>
      </div>

      {/* Pression atmosphérique */}
      <div className="border-t pt-3">
        <div className="flex items-center space-x-3">
          <div className="p-2 rounded-lg bg-gray-50 text-gray-600">
            <Gauge className="h-4 w-4" />
          </div>
          <div>
            <div className="text-sm text-gray-600">Pression</div>
            <div className="font-semibold text-gray-800">
              {weather.pressure} hPa
            </div>
          </div>
        </div>
      </div>

      {/* Indicateurs d'impact sur les vols */}
      <div className="border-t pt-3">
        <h4 className="text-sm font-medium text-gray-700 mb-2">Impact sur les opérations</h4>
        <div className="space-y-2">
          {/* Conditions de vol */}
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Conditions de vol</span>
            <span className={`text-sm font-medium px-2 py-1 rounded ${
              weather.visibility > 10 && weather.windSpeed < 25 
                ? 'bg-green-100 text-green-800' 
                : weather.visibility > 5 && weather.windSpeed < 35
                ? 'bg-yellow-100 text-yellow-800'
                : 'bg-red-100 text-red-800'
            }`}>
              {weather.visibility > 10 && weather.windSpeed < 25 
                ? 'Excellentes' 
                : weather.visibility > 5 && weather.windSpeed < 35
                ? 'Correctes'
                : 'Difficiles'
              }
            </span>
          </div>

          {/* Efficacité largage */}
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Efficacité largage</span>
            <span className={`text-sm font-medium px-2 py-1 rounded ${
              weather.windSpeed < 15 
                ? 'bg-green-100 text-green-800' 
                : weather.windSpeed < 25
                ? 'bg-yellow-100 text-yellow-800'
                : 'bg-red-100 text-red-800'
            }`}>
              {weather.windSpeed < 15 
                ? 'Optimale' 
                : weather.windSpeed < 25
                ? 'Réduite'
                : 'Compromise'
              }
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}
