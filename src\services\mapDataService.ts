import { RiskZone, WeatherData, FlightPath } from '../types';

/**
 * Service pour récupérer les données cartographiques avancées
 */

// Données simulées de zones de risque incendie
const mockRiskZones: RiskZone[] = [
  {
    id: 'zone-1',
    nom: 'Massif des Cévennes',
    type: 'tres_eleve',
    coordinates: [
      [44.1, 3.8],
      [44.2, 3.9],
      [44.1, 4.0],
      [44.0, 3.9],
      [44.1, 3.8]
    ],
    description: 'Zone de végétation dense, risque très élevé en période sèche',
    facteurRisque: 2.8
  },
  {
    id: 'zone-2',
    nom: 'Garrigue Nîmoise',
    type: 'eleve',
    coordinates: [
      [43.7, 4.2],
      [43.9, 4.4],
      [43.8, 4.6],
      [43.6, 4.4],
      [43.7, 4.2]
    ],
    description: 'Végétation méditerranéenne, risque élevé',
    facteurRisque: 2.2
  },
  {
    id: 'zone-3',
    nom: 'Plaine de la Camargue',
    type: 'modere',
    coordinates: [
      [43.4, 4.3],
      [43.6, 4.5],
      [43.5, 4.7],
      [43.3, 4.5],
      [43.4, 4.3]
    ],
    description: 'Zone humide, risque modéré',
    facteurRisque: 1.5
  },
  {
    id: 'zone-4',
    nom: 'Littoral Héraultais',
    type: 'faible',
    coordinates: [
      [43.5, 3.7],
      [43.7, 3.9],
      [43.6, 4.1],
      [43.4, 3.9],
      [43.5, 3.7]
    ],
    description: 'Zone côtière, risque faible',
    facteurRisque: 1.2
  }
];

/**
 * Récupère les zones de risque incendie
 */
export async function getRiskZones(): Promise<RiskZone[]> {
  // Simulation d'un appel API
  return new Promise((resolve) => {
    setTimeout(() => resolve(mockRiskZones), 500);
  });
}

/**
 * Récupère les données météo actuelles pour une position
 */
export async function getWeatherData(lat: number, lon: number): Promise<WeatherData> {
  // Simulation d'un appel API météo
  return new Promise((resolve) => {
    setTimeout(() => {
      const weather: WeatherData = {
        temperature: Math.round(15 + Math.random() * 20), // 15-35°C
        humidity: Math.round(30 + Math.random() * 50), // 30-80%
        windSpeed: Math.round(Math.random() * 30), // 0-30 km/h
        windDirection: Math.round(Math.random() * 360), // 0-360°
        visibility: Math.round(5 + Math.random() * 15), // 5-20 km
        pressure: Math.round(1000 + Math.random() * 50), // 1000-1050 hPa
        timestamp: new Date().toISOString()
      };
      resolve(weather);
    }, 300);
  });
}

/**
 * Calcule les trajectoires de vol optimisées
 */
export function calculateFlightPaths(
  startLat: number,
  startLon: number,
  targetLat: number,
  targetLon: number,
  aircraftType: 'canadair' | 'dash'
): FlightPath {
  // Calcul simplifié d'une trajectoire optimisée
  const distance = calculateDistance(startLat, startLon, targetLat, targetLon);
  
  // Points intermédiaires pour une trajectoire réaliste
  const midLat = (startLat + targetLat) / 2;
  const midLon = (startLon + targetLon) / 2;
  
  // Ajout d'une légère courbure pour éviter les obstacles
  const offsetLat = (Math.random() - 0.5) * 0.02;
  const offsetLon = (Math.random() - 0.5) * 0.02;
  
  const coordinates: [number, number][] = [
    [startLat, startLon],
    [midLat + offsetLat, midLon + offsetLon],
    [targetLat, targetLon]
  ];
  
  // Calculs spécifiques selon le type d'appareil
  const speeds = {
    canadair: 300, // km/h
    dash: 400 // km/h
  };
  
  const fuelConsumption = {
    canadair: 1200, // L/h
    dash: 800 // L/h
  };
  
  const speed = speeds[aircraftType];
  const estimatedTime = (distance / speed) * 60; // minutes
  const fuel = (fuelConsumption[aircraftType] * estimatedTime) / 60; // litres
  
  return {
    id: `path-${aircraftType}-${Date.now()}`,
    type: aircraftType,
    coordinates,
    distance: Math.round(distance * 100) / 100,
    estimatedTime: Math.round(estimatedTime),
    fuelConsumption: Math.round(fuel)
  };
}

/**
 * Calcule la distance entre deux points (formule de Haversine)
 */
function calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
  const R = 6371; // Rayon de la Terre en km
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lon2 - lon1) * Math.PI / 180;
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c;
}

/**
 * Détermine le niveau de risque pour une zone donnée
 */
export function getRiskLevel(lat: number, lon: number): RiskZone | null {
  // Vérifie si le point est dans une zone de risque
  for (const zone of mockRiskZones) {
    if (isPointInPolygon(lat, lon, zone.coordinates)) {
      return zone;
    }
  }
  return null;
}

/**
 * Vérifie si un point est dans un polygone (algorithme ray casting)
 */
function isPointInPolygon(lat: number, lon: number, polygon: [number, number][]): boolean {
  let inside = false;
  for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
    const [xi, yi] = polygon[i];
    const [xj, yj] = polygon[j];
    
    if (((yi > lon) !== (yj > lon)) && (lat < (xj - xi) * (lon - yi) / (yj - yi) + xi)) {
      inside = !inside;
    }
  }
  return inside;
}

/**
 * Génère des données météo historiques simulées
 */
export async function getHistoricalWeather(lat: number, lon: number, days: number = 7): Promise<WeatherData[]> {
  const data: WeatherData[] = [];
  const now = new Date();
  
  for (let i = 0; i < days; i++) {
    const date = new Date(now.getTime() - (i * 24 * 60 * 60 * 1000));
    data.push({
      temperature: Math.round(15 + Math.random() * 20),
      humidity: Math.round(30 + Math.random() * 50),
      windSpeed: Math.round(Math.random() * 30),
      windDirection: Math.round(Math.random() * 360),
      visibility: Math.round(5 + Math.random() * 15),
      pressure: Math.round(1000 + Math.random() * 50),
      timestamp: date.toISOString()
    });
  }
  
  return data.reverse(); // Plus ancien en premier
}
